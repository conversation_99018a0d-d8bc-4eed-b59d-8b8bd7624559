"""
Main Lambda handler for processing agent events from SQS.

This module provides the entry point for the AWS Lambda function that processes
agent events from SQS queues and populates Redshift dimension tables.
"""

import json
from typing import Any, Dict, List, Optional

from aws_lambda_typing import <PERSON>daContext
from aws_lambda_typing.events import SQ<PERSON><PERSON>

from .config import configure_logging, get_settings
from .config.logging_config import add_correlation_context, get_logger
from .services.event_processor import EventProcessor
from .utils.metrics import MetricsCollector

# Configure logging on module import
configure_logging()
logger = get_logger(__name__)

# Global variables for Lambda container reuse
_event_processor: Optional[EventProcessor] = None
_metrics_collector: Optional[MetricsCollector] = None


def get_event_processor() -> EventProcessor:
    """
    Get or create event processor instance (singleton for container reuse).
    
    Returns:
        EventProcessor: Configured event processor instance.
    """
    global _event_processor
    
    if _event_processor is None:
        settings = get_settings()
        metrics = get_metrics_collector()
        _event_processor = EventProcessor(settings, metrics)
        logger.info("Event processor initialized")
    
    return _event_processor


def get_metrics_collector() -> MetricsCollector:
    """
    Get or create metrics collector instance (singleton for container reuse).
    
    Returns:
        MetricsCollector: Configured metrics collector instance.
    """
    global _metrics_collector
    
    if _metrics_collector is None:
        settings = get_settings()
        _metrics_collector = MetricsCollector(
            namespace=settings.aws.metrics_namespace,
            enabled=settings.enable_metrics
        )
        logger.info("Metrics collector initialized")
    
    return _metrics_collector


def lambda_handler(event: SQSEvent, context: LambdaContext) -> Dict[str, Any]:
    """
    Main Lambda handler for processing agent events from SQS.
    
    This function processes batches of agent events from SQS, validates them,
    and populates the appropriate Redshift dimension and fact tables.
    
    Args:
        event: SQS event containing Records array with agent events.
        context: Lambda execution context with request metadata.
        
    Returns:
        Dict containing processing results and any failed message identifiers
        for partial batch failure handling.
        
    Raises:
        Exception: Re-raises any unhandled exceptions after logging.
    """
    # Set up correlation tracking
    correlation_id = context.aws_request_id
    request_logger = add_correlation_context(
        logger,
        correlation_id=correlation_id,
        function_name=context.function_name,
        function_version=context.function_version,
        remaining_time_ms=context.get_remaining_time_in_millis()
    )
    
    try:
        # Get processor and metrics instances
        processor = get_event_processor()
        metrics = get_metrics_collector()
        
        # Extract SQS records
        records = event.get("Records", [])
        request_logger.info(
            "Processing SQS batch",
            record_count=len(records),
            memory_limit_mb=context.memory_limit_in_mb
        )
        
        # Process the batch
        results = processor.process_batch(records, context)
        
        # Publish metrics
        metrics.put_metric("ProcessedRecords", len(records))
        metrics.put_metric("SuccessfulRecords", results["successful_count"])
        metrics.put_metric("FailedRecords", results["failed_count"])
        
        if results["failed_count"] > 0:
            metrics.put_metric("BatchPartialFailure", 1)
            request_logger.warning(
                "Batch processing completed with failures",
                successful_count=results["successful_count"],
                failed_count=results["failed_count"],
                failed_message_ids=results.get("failed_message_ids", [])
            )
        else:
            metrics.put_metric("BatchSuccess", 1)
            request_logger.info(
                "Batch processing completed successfully",
                successful_count=results["successful_count"]
            )
        
        # Flush metrics before returning
        metrics.flush_metrics()
        
        # Return response with partial batch failure support
        response = {
            "statusCode": 200,
            "body": json.dumps({
                "message": "Batch processing completed",
                "successful_count": results["successful_count"],
                "failed_count": results["failed_count"],
                "correlation_id": correlation_id
            })
        }
        
        # Include batch item failures for SQS partial batch failure
        if results.get("batch_item_failures"):
            response["batchItemFailures"] = results["batch_item_failures"]
        
        return response
        
    except Exception as e:
        # Log the error with full context
        request_logger.error(
            "Lambda execution failed",
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True
        )
        
        # Publish error metric
        metrics = get_metrics_collector()
        metrics.put_metric("LambdaErrors", 1)
        metrics.flush_metrics()
        
        # Re-raise to trigger Lambda error handling
        raise

"""
Database models and credentials for Redshift connectivity.

This module defines data models for database credentials and connection
management with proper type safety.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class DatabaseCredentials:
    """
    Database credentials retrieved from AWS Secrets Manager.

    This model validates and structures database connection credentials
    for secure Redshift connectivity.
    """

    host: str
    port: int
    database: str
    username: str
    password: str
    engine: Optional[str] = "redshift"


@dataclass
class DimensionKeys:
    """
    Dimension keys for fact table relationships.
    
    This model represents the foreign keys needed for fact table inserts
    with proper validation.
    """
    
    tenant_key: int = Field(
        ...,
        description="Foreign key to dim_tenant"
    )
    agent_key: int = Field(
        ...,
        description="Foreign key to dim_agent"
    )
    date_key: int = Field(
        ...,
        description="Foreign key to dim_date (YYYYMMDD format)"
    )
    time_key: int = Field(
        ...,
        description="Foreign key to dim_time (HHMMSS format)"
    )
    queue_key: Optional[int] = Field(
        None,
        description="Foreign key to dim_queue (for ACD events)"
    )
    
    @validator('date_key')
    def validate_date_key(cls, v: int) -> int:
        """
        Validate date key format (YYYYMMDD).
        
        Args:
            v: Date key value.
            
        Returns:
            int: Validated date key.
            
        Raises:
            ValueError: If date key format is invalid.
        """
        if not (19000101 <= v <= 21001231):
            raise ValueError("Date key must be in YYYYMMDD format between 1900-2100")
        return v
    
    @validator('time_key')
    def validate_time_key(cls, v: int) -> int:
        """
        Validate time key format (HHMMSS).
        
        Args:
            v: Time key value.
            
        Returns:
            int: Validated time key.
            
        Raises:
            ValueError: If time key format is invalid.
        """
        if not (0 <= v <= 235959):
            raise ValueError("Time key must be in HHMMSS format (000000-235959)")
        return v
    
    class Config:
        """Pydantic model configuration."""
        
        validate_assignment = True
        extra = "forbid"

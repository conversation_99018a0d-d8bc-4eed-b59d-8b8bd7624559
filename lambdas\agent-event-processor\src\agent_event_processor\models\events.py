"""
Event models for agent events with strong typing and validation.

This module defines dataclass models for all supported agent event types
with comprehensive validation and type safety.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional


class EventType(str, Enum):
    """Supported agent event types."""
    
    LOGIN = "Login"
    LOGOUT = "Logout"
    AGENT_AVAILABLE = "AgentAvailable"
    AGENT_BUSIED_OUT = "AgentBusiedOut"
    ACD_LOGIN = "ACDLogin"
    ACD_LOGOUT = "ACDLogout"


@dataclass
class AgentEvent:
    """
    Base model for all agent events with comprehensive validation.

    This model handles all agent event types and provides validation
    for required and optional fields based on the event type.
    """

    # Core event fields (required for all events)
    timestamp: datetime
    event_type: EventType
    agency_or_element: str
    agent: str

    # Common agent fields (optional, present in most events)
    media_label: Optional[str] = None
    agent_uri: Optional[str] = None
    agent_role: Optional[str] = None
    tenant_group: Optional[str] = None
    operator_id: Optional[str] = None
    workstation: Optional[str] = None
    device_name: Optional[str] = None

    # Event-specific fields
    reason: Optional[str] = None
    response_code: Optional[str] = None
    busied_out_action: Optional[str] = None
    ring_group_name: Optional[str] = None
    ring_group_uri: Optional[str] = None

    # Voice quality metrics (optional, for logout events)
    voice_qos: Optional[dict] = None

    # SQS metadata (populated during processing)
    sqs_message_id: Optional[str] = None
    sqs_receipt_handle: Optional[str] = None

    @classmethod
    def from_dict(cls, data: dict) -> 'AgentEvent':
        """
        Create AgentEvent from dictionary with basic validation.

        Args:
            data: Dictionary containing event data.

        Returns:
            AgentEvent: Validated event instance.
        """
        # Parse timestamp
        timestamp_str = data.get('timestamp')
        if isinstance(timestamp_str, str):
            if timestamp_str.endswith('Z'):
                timestamp_str = timestamp_str.replace('Z', '+00:00')
            timestamp = datetime.fromisoformat(timestamp_str)
        else:
            timestamp = timestamp_str

        # Parse event type
        event_type_str = data.get('eventType', data.get('event_type'))
        event_type = EventType(event_type_str)

        # Create instance
        return cls(
            timestamp=timestamp,
            event_type=event_type,
            agency_or_element=data.get('agencyOrElement', data.get('agency_or_element')),
            agent=data.get('agent'),
            media_label=data.get('mediaLabel'),
            agent_uri=data.get('uri'),
            agent_role=data.get('agentRole'),
            tenant_group=data.get('tenantGroup'),
            operator_id=data.get('operatorId'),
            workstation=data.get('workstation'),
            device_name=data.get('deviceName'),
            reason=data.get('reason'),
            response_code=data.get('responseCode'),
            busied_out_action=data.get('busiedOutAction'),
            ring_group_name=data.get('ringGroupName'),
            ring_group_uri=data.get('ringGroupUri'),
            voice_qos=data.get('voiceQOS'),
        )

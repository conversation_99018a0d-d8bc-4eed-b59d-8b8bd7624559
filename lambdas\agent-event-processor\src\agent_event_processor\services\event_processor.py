"""
Main event processing service for agent events.

This module orchestrates the processing of agent events from SQS,
coordinating between message parsing, database operations, and metrics.
"""

import time
from typing import Dict, Any, List

from aws_lambda_typing import LambdaContext
from aws_lambda_typing.events import SQSRecord

from ..config.logging_config import get_logger
from ..config.settings import Settings
from ..models.events import AgentEvent, EventType
from ..utils.metrics import MetricsCollector
from ..utils.timezone_utils import get_tenant_timezone, convert_to_tenant_timezone, generate_dimension_keys
from .sqs_service import SQSMessageProcessor
from .database_service import RedshiftConnectionManager, DimensionTableManager

logger = get_logger(__name__)


class EventProcessor:
    """
    Main event processor for agent events.
    
    This class orchestrates the complete processing pipeline from SQS message
    parsing through database operations and metrics publishing.
    """
    
    def __init__(self, settings: Settings, metrics: MetricsCollector):
        """
        Initialize event processor.
        
        Args:
            settings: Application configuration settings.
            metrics: Metrics collector for CloudWatch publishing.
        """
        self.settings = settings
        self.metrics = metrics
        
        # Initialize services
        self.sqs_processor = SQSMessageProcessor()
        self.connection_manager = RedshiftConnectionManager(settings.database)
        self.dimension_manager = DimensionTableManager(self.connection_manager)
        
        logger.info("Event processor initialized")
    
    def process_batch(
        self, 
        records: List[SQSRecord], 
        context: LambdaContext
    ) -> Dict[str, Any]:
        """
        Process a batch of SQS records containing agent events.
        
        Args:
            records: List of SQS records from Lambda event.
            context: Lambda execution context.
            
        Returns:
            Dict containing processing results and failure information.
        """
        start_time = time.time()
        
        logger.info(
            "Starting batch processing",
            record_count=len(records),
            remaining_time_ms=context.get_remaining_time_in_millis()
        )
        
        try:
            # Parse SQS records into events
            valid_events, failed_records = self.sqs_processor.parse_sqs_records(records)
            
            # Process valid events
            processing_results = self._process_events(valid_events, context)
            
            # Combine results
            total_successful = processing_results["successful_count"]
            total_failed = len(failed_records) + processing_results["failed_count"]
            
            # Create batch item failures for SQS
            batch_failures = self.sqs_processor.create_batch_item_failures(failed_records)
            if processing_results.get("failed_message_ids"):
                for msg_id in processing_results["failed_message_ids"]:
                    batch_failures.append({"itemIdentifier": msg_id})
            
            # Calculate processing time
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Publish performance metrics
            self.metrics.put_performance_metric(
                operation="batch_processing",
                duration_ms=processing_time_ms,
                success=total_failed == 0
            )
            
            logger.info(
                "Batch processing completed",
                total_records=len(records),
                successful_count=total_successful,
                failed_count=total_failed,
                processing_time_ms=processing_time_ms,
                remaining_time_ms=context.get_remaining_time_in_millis()
            )
            
            return {
                "successful_count": total_successful,
                "failed_count": total_failed,
                "processing_time_ms": processing_time_ms,
                "batch_item_failures": batch_failures,
                "failed_message_ids": processing_results.get("failed_message_ids", [])
            }
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            
            logger.error(
                "Batch processing failed",
                error=str(e),
                processing_time_ms=processing_time_ms,
                exc_info=True
            )
            
            # Publish error metrics
            self.metrics.put_performance_metric(
                operation="batch_processing",
                duration_ms=processing_time_ms,
                success=False
            )
            
            raise
    
    def _process_events(
        self, 
        events: List[AgentEvent], 
        context: LambdaContext
    ) -> Dict[str, Any]:
        """
        Process validated agent events and update database.
        
        Args:
            events: List of validated agent events.
            context: Lambda execution context.
            
        Returns:
            Dict containing processing results.
        """
        successful_count = 0
        failed_count = 0
        failed_message_ids = []
        
        logger.info("Processing validated events", event_count=len(events))
        
        for event in events:
            try:
                # Check remaining time
                if context.get_remaining_time_in_millis() < self.settings.aws.lambda_timeout_buffer * 1000:
                    logger.warning(
                        "Approaching Lambda timeout, stopping processing",
                        remaining_time_ms=context.get_remaining_time_in_millis()
                    )
                    break
                
                # Process individual event
                self._process_single_event(event)
                successful_count += 1
                
                # Publish business metrics
                self.metrics.put_business_metric(
                    event_type=event.event_type.value,
                    tenant=event.agency_or_element
                )
                
                logger.debug(
                    "Event processed successfully",
                    event_type=event.event_type,
                    agent=event.agent,
                    tenant=event.agency_or_element,
                    message_id=event.sqs_message_id
                )
                
            except Exception as e:
                failed_count += 1
                if event.sqs_message_id:
                    failed_message_ids.append(event.sqs_message_id)
                
                logger.error(
                    "Failed to process event",
                    event_type=event.event_type,
                    agent=event.agent,
                    tenant=event.agency_or_element,
                    message_id=event.sqs_message_id,
                    error=str(e),
                    exc_info=True
                )
        
        return {
            "successful_count": successful_count,
            "failed_count": failed_count,
            "failed_message_ids": failed_message_ids
        }
    
    def _process_single_event(self, event: AgentEvent) -> None:
        """
        Process a single agent event and update database.
        
        Args:
            event: Validated agent event to process.
            
        Raises:
            Exception: If event processing fails.
        """
        start_time = time.time()
        
        try:
            with self.connection_manager.get_connection() as conn:
                # Get or create tenant
                tenant_key = self.dimension_manager.get_or_create_tenant_key(
                    conn, 
                    event.agency_or_element,
                    get_tenant_timezone(event.agency_or_element)
                )
                
                # Convert timestamp to tenant timezone
                tenant_timezone = get_tenant_timezone(event.agency_or_element)
                local_timestamp = convert_to_tenant_timezone(
                    event.timestamp, 
                    tenant_timezone
                )
                
                # Generate dimension keys
                date_key, time_key = generate_dimension_keys(local_timestamp)
                
                # Process based on event type
                if event.event_type == EventType.LOGIN:
                    self._process_login_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                elif event.event_type == EventType.LOGOUT:
                    self._process_logout_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                elif event.event_type == EventType.AGENT_AVAILABLE:
                    self._process_agent_available_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                elif event.event_type == EventType.AGENT_BUSIED_OUT:
                    self._process_agent_busied_out_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                elif event.event_type == EventType.ACD_LOGIN:
                    self._process_acd_login_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                elif event.event_type == EventType.ACD_LOGOUT:
                    self._process_acd_logout_event(conn, event, tenant_key, local_timestamp, date_key, time_key)
                else:
                    raise ValueError(f"Unsupported event type: {event.event_type}")
                
                # Commit transaction
                conn.commit()
                
                # Calculate processing time
                processing_time_ms = (time.time() - start_time) * 1000
                
                # Publish performance metrics
                self.metrics.put_performance_metric(
                    operation=f"process_{event.event_type.value.lower()}",
                    duration_ms=processing_time_ms,
                    success=True
                )
                
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Publish error metrics
            self.metrics.put_performance_metric(
                operation=f"process_{event.event_type.value.lower()}",
                duration_ms=processing_time_ms,
                success=False
            )
            
            raise

    def _process_login_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process Login event with dimension updates and interval creation."""
        # Extract agent data
        agent_data = {
            "agent_name": event.agent,
            "operator_id": event.operator_id,
            "agent_role": event.agent_role,
            "agent_uri": event.agent_uri,
            "workstation": event.workstation,
            "event_timestamp": event.timestamp
        }

        # Upsert agent dimension
        agent_key = self.dimension_manager.upsert_agent_dimension(
            conn, agent_data, tenant_key
        )

        with conn.cursor() as cursor:
            # Insert fact_agent_event record
            cursor.execute("""
                INSERT INTO fact_agent_event (
                    agent_key, tenant_key, date_key, time_key,
                    event_timestamp_utc, event_timestamp_local,
                    event_type, reason_code, media_label,
                    workstation, device_name
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                agent_key, tenant_key, date_key, time_key,
                event.timestamp, local_timestamp,
                event.event_type.value, event.reason,
                event.media_label, event.workstation, event.device_name
            ))

            # Close any open intervals for this agent
            cursor.execute("""
                UPDATE fact_agent_intervals
                SET interval_end_utc = %s,
                    interval_end_local = %s,
                    duration_seconds = EXTRACT(EPOCH FROM (%s - interval_start_utc)),
                    is_current_interval = false
                WHERE agent_key = %s
                  AND is_current_interval = true
            """, (event.timestamp, local_timestamp, event.timestamp, agent_key))

            # Create new LoggedIn interval
            cursor.execute("""
                INSERT INTO fact_agent_intervals (
                    agent_key, tenant_key, date_key, start_time_key,
                    interval_start_utc, interval_start_local,
                    interval_type, is_current_interval
                ) VALUES (%s, %s, %s, %s, %s, %s, 'LoggedIn', true)
            """, (
                agent_key, tenant_key, date_key, time_key,
                event.timestamp, local_timestamp
            ))

    def _process_logout_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process Logout event and close open intervals."""
        # Get agent key (should exist from previous login)
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT agent_key FROM dim_agent
                WHERE tenant_key = %s AND agent_name = %s AND is_current = true
            """, (tenant_key, event.agent))

            result = cursor.fetchone()
            if not result:
                logger.warning(
                    "Agent not found for logout event",
                    agent=event.agent,
                    tenant_key=tenant_key
                )
                return

            agent_key = result[0]

            # Insert fact_agent_event record
            cursor.execute("""
                INSERT INTO fact_agent_event (
                    agent_key, tenant_key, date_key, time_key,
                    event_timestamp_utc, event_timestamp_local,
                    event_type, reason_code, media_label,
                    workstation, device_name
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                agent_key, tenant_key, date_key, time_key,
                event.timestamp, local_timestamp,
                event.event_type.value, event.response_code,
                event.media_label, event.workstation, event.device_name
            ))

            # Close all open intervals for this agent
            cursor.execute("""
                UPDATE fact_agent_intervals
                SET interval_end_utc = %s,
                    interval_end_local = %s,
                    duration_seconds = EXTRACT(EPOCH FROM (%s - interval_start_utc)),
                    is_current_interval = false
                WHERE agent_key = %s
                  AND is_current_interval = true
            """, (event.timestamp, local_timestamp, event.timestamp, agent_key))

    def _process_agent_available_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process AgentAvailable event - transition to Available state."""
        # Implementation placeholder - transitions from BusiedOut to Available
        pass

    def _process_agent_busied_out_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process AgentBusiedOut event - transition to BusiedOut state."""
        # Implementation placeholder - transitions from Available to BusiedOut
        pass

    def _process_acd_login_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process ACDLogin event - agent joins ACD queue."""
        # Implementation placeholder - creates ACD session
        pass

    def _process_acd_logout_event(
        self,
        conn,
        event: AgentEvent,
        tenant_key: int,
        local_timestamp,
        date_key: int,
        time_key: int
    ) -> None:
        """Process ACDLogout event - agent leaves ACD queue."""
        # Implementation placeholder - closes ACD session
        pass
